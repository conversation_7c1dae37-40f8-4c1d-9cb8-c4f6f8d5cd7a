<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Z-Index 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            margin: 0;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-btn {
            padding: 10px 20px;
            margin: 10px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            background: #0078d4;
            color: white;
        }
        
        .test-btn:hover {
            background: #106ebe;
        }
        
        /* 模拟项目中的高 z-index 元素 */
        .high-zindex-element {
            position: fixed;
            top: 10px;
            right: 10px;
            width: 200px;
            height: 50px;
            background: #ff6b6b;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            z-index: 10001;
            font-size: 14px;
        }
        
        /* 测试模态框样式 */
        .test-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 10010;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .test-backdrop {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
            z-index: 1;
        }
        
        .test-content {
            position: relative;
            width: 90vw;
            max-width: 500px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
            z-index: 2;
            padding: 20px;
        }
        
        .test-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 10px 0;
            font-size: 16px;
        }
        
        .test-textarea {
            width: 100%;
            height: 100px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 10px 0;
            font-size: 16px;
            resize: vertical;
        }
        
        .test-select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 10px 0;
            font-size: 16px;
        }
        
        .modal-buttons {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
        }
        
        .btn-close {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .btn-save {
            background: #0078d4;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .z-index-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="high-zindex-element">
        高 Z-Index 元素 (10001)
    </div>
    
    <div class="test-container">
        <h1>Z-Index 层级测试</h1>
        
        <div class="info">
            <h3>测试说明</h3>
            <p>这个页面用于测试模态框的 z-index 层级是否正确。</p>
            <p>右上角有一个红色元素，z-index 为 10001，模拟项目中的高层级元素。</p>
            <p>模态框的 z-index 为 10010，应该显示在所有元素之上。</p>
        </div>
        
        <div class="z-index-info">
            Z-Index 层级信息：<br>
            - 页面内容: 默认 (auto)<br>
            - 红色元素: 10001<br>
            - 模态框容器: 10010<br>
            - 模态框背景: 10010 + 1<br>
            - 模态框内容: 10010 + 2
        </div>
        
        <button class="test-btn" onclick="showTestModal()">显示测试模态框</button>
        
        <div id="testLog" style="margin-top: 20px; padding: 10px; background: #f8f9fa; border-radius: 4px; font-family: monospace; white-space: pre-wrap; max-height: 200px; overflow-y: auto;"></div>
    </div>

    <script>
        function log(message) {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function showTestModal() {
            log('显示测试模态框');
            
            const modal = document.createElement('div');
            modal.className = 'test-modal';
            modal.innerHTML = `
                <div class="test-backdrop"></div>
                <div class="test-content">
                    <h3>测试模态框</h3>
                    <p>如果您能看到并操作这个对话框，说明 z-index 设置正确。</p>
                    
                    <label>测试输入框：</label>
                    <input type="text" class="test-input" placeholder="在这里输入文本..." value="测试文本">
                    
                    <label>测试文本域：</label>
                    <textarea class="test-textarea" placeholder="在这里输入多行文本...">这是测试文本域。
您应该能够正常编辑这些内容。</textarea>
                    
                    <label>测试下拉框：</label>
                    <select class="test-select">
                        <option value="">选择选项</option>
                        <option value="option1">选项 1</option>
                        <option value="option2" selected>选项 2</option>
                        <option value="option3">选项 3</option>
                    </select>
                    
                    <div class="modal-buttons">
                        <button class="btn-close" onclick="closeTestModal()">关闭</button>
                        <button class="btn-save" onclick="saveTest()">保存测试</button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // 防止内容点击事件冒泡
            const content = modal.querySelector('.test-content');
            content.addEventListener('click', (e) => {
                e.stopPropagation();
                log('点击模态框内容');
            });
            
            // 点击背景关闭
            const backdrop = modal.querySelector('.test-backdrop');
            backdrop.addEventListener('click', (e) => {
                if (e.target === backdrop) {
                    log('点击背景关闭模态框');
                    closeTestModal();
                }
            });
            
            // 聚焦到输入框
            setTimeout(() => {
                const input = modal.querySelector('.test-input');
                if (input) {
                    input.focus();
                    input.select();
                    log('聚焦到输入框');
                }
            }, 100);
            
            window.currentModal = modal;
        }
        
        function closeTestModal() {
            if (window.currentModal) {
                document.body.removeChild(window.currentModal);
                window.currentModal = null;
                log('关闭测试模态框');
            }
        }
        
        function saveTest() {
            const modal = window.currentModal;
            if (!modal) return;
            
            const input = modal.querySelector('.test-input');
            const textarea = modal.querySelector('.test-textarea');
            const select = modal.querySelector('.test-select');
            
            const data = {
                input: input.value,
                textarea: textarea.value,
                select: select.value
            };
            
            log('保存测试数据: ' + JSON.stringify(data, null, 2));
            closeTestModal();
        }
        
        // ESC 键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && window.currentModal) {
                log('ESC 键关闭模态框');
                closeTestModal();
            }
        });
        
        // 页面加载完成
        document.addEventListener('DOMContentLoaded', () => {
            log('Z-Index 测试页面加载完成');
            log('点击按钮测试模态框层级...');
        });
    </script>
</body>
</html>
