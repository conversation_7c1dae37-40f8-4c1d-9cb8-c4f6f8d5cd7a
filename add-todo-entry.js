/**
 * 手动添加 ToDo 入口到侧边栏的脚本
 * 在浏览器控制台中运行此脚本
 */

(function() {
    console.log('开始添加 ToDo 入口到侧边栏...');
    
    // 查找侧边栏容器
    const categoryList = document.getElementById('categoryList');
    if (!categoryList) {
        console.error('未找到 categoryList 元素');
        return;
    }
    
    // 检查是否已存在 ToDo 入口
    const existingTodo = categoryList.querySelector('[data-category-id="todo"]');
    if (existingTodo) {
        console.log('ToDo 入口已存在');
        return;
    }
    
    // 创建 ToDo 入口元素
    const todoItem = document.createElement('li');
    todoItem.className = 'category-item';
    todoItem.innerHTML = `
        <a href="#" class="category-link" 
           data-category-id="todo"
           data-has-children="false" 
           data-level="0" 
           role="button" 
           tabindex="0" 
           aria-label="任务管理">
            <span class="category-icon" aria-hidden="true">📋</span>
            <span class="category-name">任务管理</span>
            <span class="category-count">0</span>
        </a>
    `;
    
    // 找到合适的插入位置（在"常用"之后）
    const frequentItem = categoryList.querySelector('[data-category-id="frequent"]');
    if (frequentItem && frequentItem.parentNode) {
        frequentItem.parentNode.insertBefore(todoItem, frequentItem.nextSibling);
        console.log('ToDo 入口已添加到"常用"之后');
    } else {
        // 如果没找到"常用"，就添加到开头
        categoryList.insertBefore(todoItem, categoryList.firstChild);
        console.log('ToDo 入口已添加到侧边栏开头');
    }
    
    // 添加点击事件处理
    const todoLink = todoItem.querySelector('.category-link');
    todoLink.addEventListener('click', function(e) {
        e.preventDefault();
        console.log('ToDo 入口被点击');
        
        // 移除其他活动状态
        document.querySelectorAll('.category-link.active').forEach(link => {
            link.classList.remove('active');
        });
        
        // 设置当前为活动状态
        this.classList.add('active');
        
        // 显示 ToDo 界面
        showTodoInterface();
    });
    
    console.log('ToDo 入口添加完成');
})();

/**
 * 显示 ToDo 界面
 */
function showTodoInterface() {
    console.log('显示 ToDo 界面');
    
    // 隐藏主内容区域
    const contentArea = document.querySelector('.content-area');
    if (contentArea) {
        contentArea.style.display = 'none';
    }
    
    // 查找或创建 ToDo 容器
    let todoContainer = document.getElementById('todoContainer');
    if (!todoContainer) {
        todoContainer = document.createElement('div');
        todoContainer.id = 'todoContainer';
        todoContainer.className = 'todo-container';
        
        // 插入到主容器中
        const mainContainer = document.querySelector('.main-container');
        if (mainContainer) {
            mainContainer.appendChild(todoContainer);
        } else {
            document.body.appendChild(todoContainer);
        }
    }
    
    // 显示 ToDo 容器
    todoContainer.style.display = 'block';
    
    // 渲染 ToDo 界面
    renderTodoInterface(todoContainer);
}

/**
 * 渲染 ToDo 界面
 */
function renderTodoInterface(container) {
    container.innerHTML = `
        <div class="todo-header">
            <div class="todo-title">
                <h2><i class="fas fa-tasks"></i> 任务管理</h2>
                <div class="todo-stats">
                    <span class="stat-item">
                        <span class="stat-number">0</span>
                        <span class="stat-label">活动任务</span>
                    </span>
                    <span class="stat-item">
                        <span class="stat-number">0</span>
                        <span class="stat-label">已完成</span>
                    </span>
                </div>
            </div>
            <div class="todo-actions">
                <button class="btn-primary add-task-btn">
                    <i class="fas fa-plus"></i> 新建任务
                </button>
                <button class="btn-secondary todo-settings-btn">
                    <i class="fas fa-cog"></i>
                </button>
            </div>
        </div>
        
        <div class="todo-content">
            <div class="todo-sidebar">
                <nav class="todo-nav">
                    <ul class="todo-nav-list">
                        <li class="nav-item active" data-view="today">
                            <i class="fas fa-calendar-day"></i>
                            <span>今天</span>
                            <span class="nav-count">0</span>
                        </li>
                        <li class="nav-item" data-view="important">
                            <i class="fas fa-star"></i>
                            <span>重要</span>
                            <span class="nav-count">0</span>
                        </li>
                        <li class="nav-item" data-view="planned">
                            <i class="fas fa-calendar-alt"></i>
                            <span>已计划</span>
                            <span class="nav-count">0</span>
                        </li>
                        <li class="nav-item" data-view="all">
                            <i class="fas fa-list"></i>
                            <span>全部任务</span>
                            <span class="nav-count">0</span>
                        </li>
                    </ul>
                    
                    <div class="todo-lists">
                        <div class="lists-header">
                            <h4>我的列表</h4>
                            <button class="add-list-btn">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                        <ul class="lists-container">
                            <li class="list-item active" data-list-id="default">
                                <i class="fas fa-list" style="color: #0078d4"></i>
                                <span class="list-name">我的任务</span>
                                <span class="list-count">0</span>
                            </li>
                        </ul>
                    </div>
                </nav>
            </div>
            
            <div class="todo-main">
                <div class="todo-view-header">
                    <h3 class="view-title">今天</h3>
                    <div class="view-actions">
                        <button class="sort-btn" title="排序">
                            <i class="fas fa-sort"></i>
                        </button>
                        <button class="filter-btn" title="筛选">
                            <i class="fas fa-filter"></i>
                        </button>
                    </div>
                </div>
                
                <div class="tasks-container">
                    <div class="empty-state">
                        <i class="fas fa-tasks fa-3x"></i>
                        <h3>暂无任务</h3>
                        <p>点击"新建任务"开始添加您的第一个任务</p>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // 绑定事件
    bindTodoEvents(container);
    
    console.log('ToDo 界面渲染完成');
}

/**
 * 绑定 ToDo 事件
 */
function bindTodoEvents(container) {
    // 新建任务按钮
    const addTaskBtn = container.querySelector('.add-task-btn');
    if (addTaskBtn) {
        addTaskBtn.addEventListener('click', () => {
            alert('新建任务功能开发中...\n\n当前显示的是 ToDo 界面的基础布局。\n完整功能正在开发中，包括：\n- 任务创建和编辑\n- 子步骤管理\n- 倒计时功能\n- Markdown 备忘\n- 数据导入导出');
        });
    }
    
    // 导航切换
    const navItems = container.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.addEventListener('click', (e) => {
            // 移除其他活动状态
            navItems.forEach(nav => nav.classList.remove('active'));
            // 设置当前为活动状态
            e.currentTarget.classList.add('active');
            
            const view = e.currentTarget.dataset.view;
            const viewTitle = container.querySelector('.view-title');
            if (viewTitle) {
                const titles = {
                    'today': '今天',
                    'important': '重要',
                    'planned': '已计划',
                    'all': '全部任务'
                };
                viewTitle.textContent = titles[view] || '任务';
            }
            
            console.log(`切换到视图: ${view}`);
        });
    });
    
    // 设置按钮
    const settingsBtn = container.querySelector('.todo-settings-btn');
    if (settingsBtn) {
        settingsBtn.addEventListener('click', () => {
            alert('设置功能开发中...');
        });
    }
}

/**
 * 隐藏 ToDo 界面
 */
function hideTodoInterface() {
    const todoContainer = document.getElementById('todoContainer');
    if (todoContainer) {
        todoContainer.style.display = 'none';
    }
    
    const contentArea = document.querySelector('.content-area');
    if (contentArea) {
        contentArea.style.display = 'block';
    }
}

// 导出函数供控制台使用
window.showTodoInterface = showTodoInterface;
window.hideTodoInterface = hideTodoInterface;

console.log('ToDo 入口脚本加载完成');
console.log('可用函数: showTodoInterface(), hideTodoInterface()');
