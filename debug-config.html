<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <h1>FaciShare 导航配置调试</h1>
        
        <div class="section">
            <h3>配置文件加载状态</h3>
            <div id="configStatus" class="status">检查中...</div>
            <pre id="configContent">等待加载...</pre>
        </div>
        
        <div class="section">
            <h3>ToDo 模块配置</h3>
            <div id="todoStatus" class="status">检查中...</div>
            <pre id="todoConfig">等待加载...</pre>
        </div>
        
        <div class="section">
            <h3>JavaScript 类检查</h3>
            <div id="jsStatus" class="status">检查中...</div>
            <pre id="jsClasses">等待检查...</pre>
        </div>
        
        <div class="section">
            <h3>NavApp 实例状态</h3>
            <div id="navAppStatus" class="status">检查中...</div>
            <pre id="navAppInfo">等待检查...</pre>
        </div>
    </div>

    <script>
        async function checkConfig() {
            const configStatus = document.getElementById('configStatus');
            const configContent = document.getElementById('configContent');
            
            try {
                const response = await fetch('./nav/data/appconfig.json?v=' + Date.now());
                if (response.ok) {
                    const config = await response.json();
                    configStatus.textContent = '配置文件加载成功';
                    configStatus.className = 'status success';
                    configContent.textContent = JSON.stringify(config, null, 2);
                    
                    // 检查 ToDo 配置
                    checkTodoConfig(config);
                } else {
                    configStatus.textContent = '配置文件加载失败: ' + response.status;
                    configStatus.className = 'status error';
                    configContent.textContent = '无法加载配置文件';
                }
            } catch (error) {
                configStatus.textContent = '配置文件加载错误: ' + error.message;
                configStatus.className = 'status error';
                configContent.textContent = error.stack;
            }
        }
        
        function checkTodoConfig(config) {
            const todoStatus = document.getElementById('todoStatus');
            const todoConfig = document.getElementById('todoConfig');
            
            if (config.todoModule) {
                if (config.todoModule.enabled) {
                    todoStatus.textContent = 'ToDo 模块已启用';
                    todoStatus.className = 'status success';
                } else {
                    todoStatus.textContent = 'ToDo 模块已禁用';
                    todoStatus.className = 'status warning';
                }
                todoConfig.textContent = JSON.stringify(config.todoModule, null, 2);
            } else {
                todoStatus.textContent = 'ToDo 模块配置不存在';
                todoStatus.className = 'status error';
                todoConfig.textContent = '配置中没有找到 todoModule 字段';
            }
        }
        
        function checkJavaScriptClasses() {
            const jsStatus = document.getElementById('jsStatus');
            const jsClasses = document.getElementById('jsClasses');
            
            const classes = {
                'TodoManager': typeof TodoManager !== 'undefined',
                'TodoStorage': typeof TodoStorage !== 'undefined',
                'TodoUtils': typeof TodoUtils !== 'undefined'
            };
            
            const allLoaded = Object.values(classes).every(loaded => loaded);
            
            if (allLoaded) {
                jsStatus.textContent = '所有 ToDo 类已加载';
                jsStatus.className = 'status success';
            } else {
                jsStatus.textContent = '部分 ToDo 类未加载';
                jsStatus.className = 'status warning';
            }
            
            jsClasses.textContent = JSON.stringify(classes, null, 2);
        }
        
        function checkNavApp() {
            const navAppStatus = document.getElementById('navAppStatus');
            const navAppInfo = document.getElementById('navAppInfo');
            
            if (typeof window.navApp !== 'undefined') {
                const info = {
                    exists: true,
                    hasConfig: !!window.navApp.config,
                    hasTodoManager: !!window.navApp.todoManager,
                    configTodoEnabled: window.navApp.config?.todoModule?.enabled,
                    todoManagerType: typeof window.navApp.todoManager
                };
                
                if (info.hasTodoManager) {
                    navAppStatus.textContent = 'NavApp 和 TodoManager 都已初始化';
                    navAppStatus.className = 'status success';
                } else if (info.hasConfig && info.configTodoEnabled) {
                    navAppStatus.textContent = 'NavApp 已初始化，但 TodoManager 未创建';
                    navAppStatus.className = 'status warning';
                } else {
                    navAppStatus.textContent = 'NavApp 已初始化，ToDo 模块未启用';
                    navAppStatus.className = 'status warning';
                }
                
                navAppInfo.textContent = JSON.stringify(info, null, 2);
            } else {
                navAppStatus.textContent = 'NavApp 未初始化';
                navAppStatus.className = 'status error';
                navAppInfo.textContent = 'window.navApp 不存在';
            }
        }
        
        // 页面加载时检查
        document.addEventListener('DOMContentLoaded', () => {
            checkConfig();
            checkJavaScriptClasses();
            
            // 等待一段时间后检查 NavApp（因为它可能需要时间初始化）
            setTimeout(checkNavApp, 2000);
            
            // 定期检查 NavApp 状态
            setInterval(checkNavApp, 5000);
        });
    </script>
</body>
</html>
