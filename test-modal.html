<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模态框测试</title>
    <link rel="stylesheet" href="nav/fontawesome/css/all.min.css">
    <link rel="stylesheet" href="nav/css/todo.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
            margin: 0;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-btn {
            padding: 10px 20px;
            margin: 10px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.2s ease;
        }
        .test-btn.primary {
            background: #0078d4;
            color: white;
        }
        .test-btn.primary:hover {
            background: #106ebe;
        }
        .test-btn.secondary {
            background: #6c757d;
            color: white;
        }
        .test-btn.secondary:hover {
            background: #545b62;
        }
        .info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .info h3 {
            margin-top: 0;
            color: #1976d2;
        }
        
        /* 确保 CSS 变量定义 */
        :root {
            --todo-primary-color: #0078d4;
            --todo-primary-hover: #106ebe;
            --todo-modal-bg: #ffffff;
            --todo-text-primary: #212529;
            --todo-text-secondary: #6c757d;
            --todo-border-color: #dee2e6;
            --todo-input-bg: #ffffff;
            --todo-shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);
            --todo-hover-bg: #f5f5f5;
            --todo-header-bg: #ffffff;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-vial"></i> 模态框测试</h1>
        
        <div class="info">
            <h3>测试说明</h3>
            <p>这个页面用于测试 ToDo 模块的模态框功能，验证遮罩层和交互是否正常工作。</p>
            <ul>
                <li>点击"测试模态框"按钮打开对话框</li>
                <li>验证可以正常输入和选择</li>
                <li>验证点击背景可以关闭对话框</li>
                <li>验证点击关闭按钮可以关闭对话框</li>
            </ul>
        </div>
        
        <div>
            <button class="test-btn primary" onclick="showTestModal()">
                <i class="fas fa-plus"></i> 测试模态框
            </button>
            <button class="test-btn secondary" onclick="showMultipleModals()">
                <i class="fas fa-layer-group"></i> 测试多层模态框
            </button>
        </div>
        
        <div id="testOutput" style="margin-top: 20px; padding: 10px; background: #f8f9fa; border-radius: 4px; font-family: monospace; white-space: pre-wrap;"></div>
    </div>

    <script>
        let modalCount = 0;
        
        function log(message) {
            const output = document.getElementById('testOutput');
            const timestamp = new Date().toLocaleTimeString();
            output.textContent += `[${timestamp}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }
        
        function showTestModal() {
            modalCount++;
            const modalId = `test-modal-${modalCount}`;
            
            log(`显示测试模态框 #${modalCount}`);
            
            const modal = document.createElement('div');
            modal.className = 'task-modal';
            modal.id = modalId;
            modal.innerHTML = `
                <div class="modal-backdrop"></div>
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>测试模态框 #${modalCount}</h3>
                        <button class="modal-close-btn" onclick="closeModal('${modalId}')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form class="task-form">
                            <div class="form-group">
                                <label for="testTitle${modalCount}">测试输入框</label>
                                <input type="text" id="testTitle${modalCount}" name="title" 
                                       placeholder="在这里输入文本测试..." 
                                       value="测试文本 ${modalCount}">
                            </div>
                            
                            <div class="form-group">
                                <label for="testDescription${modalCount}">测试文本域</label>
                                <textarea id="testDescription${modalCount}" name="description" 
                                          placeholder="在这里输入多行文本测试...">这是测试文本域的内容。
可以输入多行文本。
模态框应该可以正常交互。</textarea>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="testSelect${modalCount}">测试下拉框</label>
                                    <select id="testSelect${modalCount}" name="priority">
                                        <option value="">选择选项</option>
                                        <option value="option1">选项 1</option>
                                        <option value="option2" selected>选项 2</option>
                                        <option value="option3">选项 3</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="testDate${modalCount}">测试日期</label>
                                    <input type="datetime-local" id="testDate${modalCount}" name="dueDate">
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="important" checked>
                                    <span class="checkmark"></span>
                                    测试复选框
                                </label>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn-secondary" onclick="closeModal('${modalId}')">取消</button>
                        <button type="button" class="btn-primary" onclick="saveTest('${modalId}')">保存测试</button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // 防止模态框内容的点击事件冒泡
            const modalContent = modal.querySelector('.modal-content');
            modalContent.addEventListener('click', (e) => {
                e.stopPropagation();
                log(`点击模态框内容 #${modalCount}`);
            });
            
            // 点击背景关闭
            const backdrop = modal.querySelector('.modal-backdrop');
            backdrop.addEventListener('click', (e) => {
                if (e.target === backdrop) {
                    log(`点击背景关闭模态框 #${modalCount}`);
                    closeModal(modalId);
                }
            });
            
            // 聚焦到输入框
            setTimeout(() => {
                const input = modal.querySelector(`#testTitle${modalCount}`);
                if (input) {
                    input.focus();
                    input.select();
                }
            }, 100);
        }
        
        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                document.body.removeChild(modal);
                log(`关闭模态框: ${modalId}`);
            }
        }
        
        function saveTest(modalId) {
            const modal = document.getElementById(modalId);
            const form = modal.querySelector('.task-form');
            const formData = new FormData(form);
            
            const data = {
                title: formData.get('title'),
                description: formData.get('description'),
                priority: formData.get('priority'),
                dueDate: formData.get('dueDate'),
                important: formData.has('important')
            };
            
            log(`保存测试数据: ${JSON.stringify(data, null, 2)}`);
            closeModal(modalId);
        }
        
        function showMultipleModals() {
            log('显示多层模态框测试');
            showTestModal();
            setTimeout(() => showTestModal(), 500);
        }
        
        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('模态框测试页面加载完成');
            log('点击按钮开始测试...');
        });
        
        // ESC 键关闭最新的模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const modals = document.querySelectorAll('.task-modal');
                if (modals.length > 0) {
                    const lastModal = modals[modals.length - 1];
                    closeModal(lastModal.id);
                    log('ESC 键关闭模态框');
                }
            }
        });
    </script>
</body>
</html>
