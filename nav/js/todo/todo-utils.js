/**
 * ToDo 工具函数集合
 * 提供通用的工具方法和辅助函数
 */
class TodoUtils {
    /**
     * 生成唯一ID
     */
    static generateId() {
        return 'todo-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    }
    
    /**
     * 生成导入批次ID
     */
    static generateImportId() {
        return 'import-' + Date.now() + '-' + Math.random().toString(36).substr(2, 6);
    }
    
    /**
     * 深度克隆对象
     */
    static deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => TodoUtils.deepClone(item));
        if (typeof obj === 'object') {
            const cloned = {};
            Object.keys(obj).forEach(key => {
                cloned[key] = TodoUtils.deepClone(obj[key]);
            });
            return cloned;
        }
    }
    
    /**
     * 格式化日期
     */
    static formatDate(date, format = 'YYYY-MM-DD HH:mm') {
        if (!date) return '';
        
        const d = new Date(date);
        if (isNaN(d.getTime())) return '';
        
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    }
    
    /**
     * 格式化相对时间
     */
    static formatRelativeTime(date) {
        if (!date) return '';
        
        const now = new Date();
        const target = new Date(date);
        const diffMs = target - now;
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffMinutes = Math.floor(diffMs / (1000 * 60));
        
        if (diffMs < 0) {
            // 已过期
            const absDays = Math.abs(diffDays);
            const absHours = Math.abs(diffHours);
            const absMinutes = Math.abs(diffMinutes);
            
            if (absDays > 0) return `已逾期 ${absDays} 天`;
            if (absHours > 0) return `已逾期 ${absHours} 小时`;
            if (absMinutes > 0) return `已逾期 ${absMinutes} 分钟`;
            return '刚刚逾期';
        } else {
            // 未到期
            if (diffDays > 7) return `还有 ${diffDays} 天`;
            if (diffDays > 0) return `还有 ${diffDays} 天`;
            if (diffHours > 0) return `还有 ${diffHours} 小时`;
            if (diffMinutes > 0) return `还有 ${diffMinutes} 分钟`;
            return '即将到期';
        }
    }
    
    /**
     * 获取今天的日期范围
     */
    static getTodayRange() {
        const today = new Date();
        const start = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        const end = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);
        
        return {
            start: start.toISOString(),
            end: end.toISOString()
        };
    }
    
    /**
     * 获取本周的日期范围
     */
    static getThisWeekRange() {
        const today = new Date();
        const dayOfWeek = today.getDay();
        const start = new Date(today.getFullYear(), today.getMonth(), today.getDate() - dayOfWeek);
        const end = new Date(today.getFullYear(), today.getMonth(), today.getDate() + (7 - dayOfWeek));
        
        return {
            start: start.toISOString(),
            end: end.toISOString()
        };
    }
    
    /**
     * 检查日期是否在今天
     */
    static isToday(date) {
        if (!date) return false;
        
        const today = new Date();
        const target = new Date(date);
        
        return today.getFullYear() === target.getFullYear() &&
               today.getMonth() === target.getMonth() &&
               today.getDate() === target.getDate();
    }
    
    /**
     * 检查日期是否在本周
     */
    static isThisWeek(date) {
        if (!date) return false;
        
        const weekRange = TodoUtils.getThisWeekRange();
        const target = new Date(date);
        
        return target >= new Date(weekRange.start) && target < new Date(weekRange.end);
    }
    
    /**
     * 检查日期是否已逾期
     */
    static isOverdue(date) {
        if (!date) return false;
        
        const now = new Date();
        const target = new Date(date);
        
        return target < now;
    }
    
    /**
     * 计算任务进度百分比
     */
    static calculateProgress(task) {
        if (!task.subtasks?.enabled || !task.steps || task.steps.length === 0) {
            return task.completed ? 100 : 0;
        }
        
        let totalWeight = 0;
        let completedWeight = 0;
        
        const calculateStepProgress = (steps) => {
            steps.forEach(step => {
                const weight = step.weight || 0;
                totalWeight += weight;
                
                if (step.completed) {
                    completedWeight += weight;
                }
                
                // 递归处理子步骤
                if (step.substeps && step.substeps.length > 0) {
                    calculateStepProgress(step.substeps);
                }
            });
        };
        
        calculateStepProgress(task.steps);
        
        return totalWeight > 0 ? Math.round((completedWeight / totalWeight) * 100) : 0;
    }
    
    /**
     * 获取任务紧急程度
     */
    static getUrgencyLevel(task) {
        if (!task.dueDate) return 'normal';
        
        const now = new Date();
        const dueDate = new Date(task.dueDate);
        const hoursUntilDue = (dueDate - now) / (1000 * 60 * 60);
        
        if (hoursUntilDue < 0) return 'overdue';
        if (hoursUntilDue <= 24) return 'urgent';
        if (hoursUntilDue <= 168) return 'warning'; // 7天
        return 'normal';
    }
    
    /**
     * 获取优先级权重
     */
    static getPriorityWeight(priority) {
        const weights = {
            'low': 1,
            'medium': 2,
            'high': 3,
            'urgent': 4
        };
        return weights[priority] || 2;
    }
    
    /**
     * 排序任务
     */
    static sortTasks(tasks, sortBy = 'dueDate') {
        const sorted = [...tasks];
        
        sorted.sort((a, b) => {
            switch (sortBy) {
                case 'priority':
                    const aPriority = TodoUtils.getPriorityWeight(a.priority?.level);
                    const bPriority = TodoUtils.getPriorityWeight(b.priority?.level);
                    return bPriority - aPriority;
                    
                case 'dueDate':
                    if (!a.dueDate && !b.dueDate) return 0;
                    if (!a.dueDate) return 1;
                    if (!b.dueDate) return -1;
                    return new Date(a.dueDate) - new Date(b.dueDate);
                    
                case 'created':
                    return new Date(b.createdAt) - new Date(a.createdAt);
                    
                case 'updated':
                    return new Date(b.updatedAt) - new Date(a.updatedAt);
                    
                case 'alphabetical':
                    return a.title.localeCompare(b.title);
                    
                case 'status':
                    const statusOrder = { 'in-progress': 0, 'not-started': 1, 'completed': 2 };
                    const aStatus = statusOrder[a.status] || 1;
                    const bStatus = statusOrder[b.status] || 1;
                    return aStatus - bStatus;
                    
                default:
                    return 0;
            }
        });
        
        return sorted;
    }
    
    /**
     * 过滤任务
     */
    static filterTasks(tasks, filters = {}) {
        return tasks.filter(task => {
            // 状态过滤
            if (filters.status && task.status !== filters.status) {
                return false;
            }
            
            // 优先级过滤
            if (filters.priority && task.priority?.level !== filters.priority) {
                return false;
            }
            
            // 列表过滤
            if (filters.listId && task.listId !== filters.listId) {
                return false;
            }
            
            // 日期范围过滤
            if (filters.dateRange) {
                if (!task.dueDate) return false;
                const dueDate = new Date(task.dueDate);
                const start = new Date(filters.dateRange.start);
                const end = new Date(filters.dateRange.end);
                if (dueDate < start || dueDate >= end) return false;
            }
            
            // 今天过滤
            if (filters.today && !TodoUtils.isToday(task.dueDate)) {
                return false;
            }
            
            // 本周过滤
            if (filters.thisWeek && !TodoUtils.isThisWeek(task.dueDate)) {
                return false;
            }
            
            // 逾期过滤
            if (filters.overdue && !TodoUtils.isOverdue(task.dueDate)) {
                return false;
            }
            
            // 重要任务过滤
            if (filters.important && !task.important) {
                return false;
            }
            
            // 已完成过滤
            if (filters.completed !== undefined && task.completed !== filters.completed) {
                return false;
            }
            
            // 归档过滤
            if (filters.archived !== undefined && task.archived !== filters.archived) {
                return false;
            }
            
            // 文本搜索
            if (filters.search) {
                const searchText = filters.search.toLowerCase();
                const taskText = `${task.title} ${task.description || ''}`.toLowerCase();
                if (!taskText.includes(searchText)) {
                    return false;
                }
            }
            
            return true;
        });
    }
    
    /**
     * 验证任务数据
     */
    static validateTask(task) {
        const errors = [];
        
        if (!task.title || task.title.trim() === '') {
            errors.push('任务标题不能为空');
        }
        
        if (task.title && task.title.length > 200) {
            errors.push('任务标题不能超过200个字符');
        }
        
        if (task.description && task.description.length > 2000) {
            errors.push('任务描述不能超过2000个字符');
        }
        
        if (task.dueDate && isNaN(new Date(task.dueDate).getTime())) {
            errors.push('截止日期格式无效');
        }
        
        if (task.priority && !['low', 'medium', 'high', 'urgent'].includes(task.priority.level)) {
            errors.push('优先级值无效');
        }
        
        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }
    
    /**
     * 计算数据校验和
     */
    static calculateChecksum(data) {
        const str = JSON.stringify(data);
        let hash = 0;
        
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        
        return Math.abs(hash).toString(36);
    }
    
    /**
     * 防抖函数
     */
    static debounce(func, wait, immediate = false) {
        let timeout;
        
        return function executedFunction(...args) {
            const later = () => {
                timeout = null;
                if (!immediate) func.apply(this, args);
            };
            
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            
            if (callNow) func.apply(this, args);
        };
    }
    
    /**
     * 节流函数
     */
    static throttle(func, limit) {
        let inThrottle;
        
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
}
