/**
 * 模态框调试脚本
 * 在浏览器控制台中运行此脚本来调试模态框问题
 */

(function() {
    console.log('=== 模态框调试脚本 ===');
    
    // 检查当前页面的 z-index 情况
    function checkZIndexElements() {
        console.log('检查页面中的高 z-index 元素...');
        
        const allElements = document.querySelectorAll('*');
        const highZIndexElements = [];
        
        allElements.forEach(el => {
            const style = window.getComputedStyle(el);
            const zIndex = parseInt(style.zIndex);
            
            if (!isNaN(zIndex) && zIndex > 1000) {
                highZIndexElements.push({
                    element: el,
                    zIndex: zIndex,
                    tagName: el.tagName,
                    className: el.className,
                    id: el.id
                });
            }
        });
        
        // 按 z-index 排序
        highZIndexElements.sort((a, b) => b.zIndex - a.zIndex);
        
        console.log('高 z-index 元素列表:');
        highZIndexElements.forEach(item => {
            console.log(`z-index: ${item.zIndex}, ${item.tagName}${item.id ? '#' + item.id : ''}${item.className ? '.' + item.className.split(' ')[0] : ''}`, item.element);
        });
        
        return highZIndexElements;
    }
    
    // 创建测试模态框
    function createTestModal() {
        console.log('创建测试模态框...');
        
        // 移除已存在的测试模态框
        const existingModal = document.getElementById('debug-test-modal');
        if (existingModal) {
            existingModal.remove();
        }
        
        const modal = document.createElement('div');
        modal.id = 'debug-test-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 10020;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(0, 0, 0, 0.5);
        `;
        
        const content = document.createElement('div');
        content.style.cssText = `
            position: relative;
            width: 500px;
            max-width: 90vw;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
            padding: 20px;
            z-index: 10021;
        `;
        
        content.innerHTML = `
            <h3 style="margin-top: 0;">调试测试模态框</h3>
            <p>如果您能看到并操作这个对话框，说明 z-index 设置正确。</p>
            <p><strong>当前模态框 z-index: 10020</strong></p>
            
            <div style="margin: 15px 0;">
                <label>测试输入框：</label><br>
                <input type="text" id="debugTestInput" style="width: 100%; padding: 8px; margin-top: 5px; border: 1px solid #ddd; border-radius: 4px;" placeholder="在这里输入文本..." value="测试文本">
            </div>
            
            <div style="margin: 15px 0;">
                <label>测试文本域：</label><br>
                <textarea id="debugTestTextarea" style="width: 100%; height: 80px; padding: 8px; margin-top: 5px; border: 1px solid #ddd; border-radius: 4px; resize: vertical;" placeholder="在这里输入多行文本...">这是测试文本域。
您应该能够正常编辑这些内容。</textarea>
            </div>
            
            <div style="margin: 15px 0;">
                <label>测试下拉框：</label><br>
                <select id="debugTestSelect" style="width: 100%; padding: 8px; margin-top: 5px; border: 1px solid #ddd; border-radius: 4px;">
                    <option value="">选择选项</option>
                    <option value="option1">选项 1</option>
                    <option value="option2" selected>选项 2</option>
                    <option value="option3">选项 3</option>
                </select>
            </div>
            
            <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                <button id="debugCloseBtn" style="background: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">关闭</button>
                <button id="debugSaveBtn" style="background: #0078d4; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">保存测试</button>
            </div>
        `;
        
        modal.appendChild(content);
        document.body.appendChild(modal);
        
        // 绑定事件
        content.addEventListener('click', (e) => {
            e.stopPropagation();
            console.log('点击模态框内容');
        });
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                console.log('点击背景关闭模态框');
                closeTestModal();
            }
        });
        
        document.getElementById('debugCloseBtn').addEventListener('click', closeTestModal);
        document.getElementById('debugSaveBtn').addEventListener('click', () => {
            const input = document.getElementById('debugTestInput');
            const textarea = document.getElementById('debugTestTextarea');
            const select = document.getElementById('debugTestSelect');
            
            const data = {
                input: input.value,
                textarea: textarea.value,
                select: select.value
            };
            
            console.log('保存测试数据:', data);
            alert('测试数据已保存到控制台');
            closeTestModal();
        });
        
        // 聚焦到输入框
        setTimeout(() => {
            const input = document.getElementById('debugTestInput');
            if (input) {
                input.focus();
                input.select();
                console.log('聚焦到输入框');
            }
        }, 100);
        
        console.log('测试模态框已创建');
    }
    
    function closeTestModal() {
        const modal = document.getElementById('debug-test-modal');
        if (modal) {
            modal.remove();
            console.log('测试模态框已关闭');
        }
    }
    
    // 检查 CSS 变量
    function checkCSSVariables() {
        console.log('检查 CSS 变量...');
        const root = document.documentElement;
        const style = window.getComputedStyle(root);
        
        const todoVars = [
            '--todo-primary-color',
            '--todo-modal-bg',
            '--todo-text-primary',
            '--todo-border-color',
            '--todo-input-bg'
        ];
        
        todoVars.forEach(varName => {
            const value = style.getPropertyValue(varName);
            console.log(`${varName}: ${value || '未定义'}`);
        });
    }
    
    // 导出函数到全局
    window.debugModal = {
        checkZIndex: checkZIndexElements,
        createTestModal: createTestModal,
        closeTestModal: closeTestModal,
        checkCSSVariables: checkCSSVariables
    };
    
    console.log('调试函数已加载:');
    console.log('- debugModal.checkZIndex() - 检查页面 z-index');
    console.log('- debugModal.createTestModal() - 创建测试模态框');
    console.log('- debugModal.closeTestModal() - 关闭测试模态框');
    console.log('- debugModal.checkCSSVariables() - 检查 CSS 变量');
    
    // 自动检查 z-index
    checkZIndexElements();
})();
