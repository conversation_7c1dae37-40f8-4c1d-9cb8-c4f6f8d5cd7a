<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>侧边栏测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .test-btn {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background: #007bff;
            color: white;
        }
        .test-btn:hover {
            background: #0056b3;
        }
        .output {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .sidebar-preview {
            border: 1px solid #ddd;
            padding: 10px;
            margin-top: 10px;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>侧边栏 ToDo 入口测试</h1>
        
        <div class="test-section">
            <h3>配置检查</h3>
            <button class="test-btn" onclick="checkConfig()">检查配置</button>
            <button class="test-btn" onclick="checkTodoConfig()">检查 ToDo 配置</button>
            <div class="output" id="configOutput">点击按钮开始测试...</div>
        </div>
        
        <div class="test-section">
            <h3>侧边栏渲染测试</h3>
            <button class="test-btn" onclick="testSidebarRender()">测试侧边栏渲染</button>
            <button class="test-btn" onclick="testTodoEntry()">测试 ToDo 入口</button>
            <div class="output" id="sidebarOutput">点击按钮开始测试...</div>
            <div class="sidebar-preview" id="sidebarPreview"></div>
        </div>
        
        <div class="test-section">
            <h3>手动添加 ToDo 入口</h3>
            <button class="test-btn" onclick="manualAddTodoEntry()">手动添加 ToDo 入口</button>
            <div class="output" id="manualOutput">点击按钮开始测试...</div>
        </div>
    </div>

    <script>
        function log(elementId, message) {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            element.textContent += `[${timestamp}] ${message}\n`;
            element.scrollTop = element.scrollHeight;
        }
        
        async function checkConfig() {
            try {
                const response = await fetch('./nav/data/appconfig.json?v=' + Date.now());
                const config = await response.json();
                
                log('configOutput', '配置文件加载成功');
                log('configOutput', `版本: ${config.version}`);
                log('configOutput', `数据源数量: ${config.dataSources?.length || 0}`);
                
                if (config.todoModule) {
                    log('configOutput', `ToDo 模块存在: enabled=${config.todoModule.enabled}`);
                    log('configOutput', `显示在侧边栏: ${config.todoModule.showInSidebar}`);
                } else {
                    log('configOutput', 'ToDo 模块配置不存在');
                }
                
            } catch (error) {
                log('configOutput', `配置检查失败: ${error.message}`);
            }
        }
        
        function checkTodoConfig() {
            const mockNavApp = {
                config: {
                    todoModule: {
                        enabled: true,
                        showInSidebar: true
                    }
                },
                todoManager: null
            };
            
            log('configOutput', '模拟 NavApp 配置:');
            log('configOutput', JSON.stringify(mockNavApp.config, null, 2));
            
            // 测试条件检查
            const condition1 = mockNavApp?.config?.todoModule?.enabled;
            const condition2 = mockNavApp?.config?.todoModule?.showInSidebar;
            
            log('configOutput', `条件检查:`);
            log('configOutput', `todoModule.enabled: ${condition1}`);
            log('configOutput', `todoModule.showInSidebar: ${condition2}`);
            log('configOutput', `应该显示 ToDo 入口: ${condition1 && condition2}`);
        }
        
        function testSidebarRender() {
            // 模拟侧边栏渲染逻辑
            const mockNavApp = {
                config: {
                    todoModule: {
                        enabled: true,
                        showInSidebar: true
                    }
                },
                todoManager: {
                    getActiveTasksCount: () => 5
                }
            };
            
            const mockSidebar = {
                navApp: mockNavApp,
                getTodoTasksCount() {
                    try {
                        if (!this.navApp || !this.navApp.todoManager) return 0;
                        return this.navApp.todoManager.getActiveTasksCount();
                    } catch (error) {
                        console.warn('获取 ToDo 任务数量失败:', error);
                        return 0;
                    }
                }
            };
            
            log('sidebarOutput', '开始测试侧边栏渲染...');
            
            // 测试 getTodoTasksCount
            const taskCount = mockSidebar.getTodoTasksCount();
            log('sidebarOutput', `getTodoTasksCount() 返回: ${taskCount}`);
            
            // 测试条件判断
            const shouldShow = mockNavApp?.config?.todoModule?.enabled && mockNavApp?.config?.todoModule?.showInSidebar;
            log('sidebarOutput', `应该显示 ToDo 入口: ${shouldShow}`);
            
            if (shouldShow) {
                const todoHtml = `
                    <li class="category-item">
                        <a href="#" class="category-link" data-category-id="todo">
                            <span class="category-icon">📋</span>
                            <span class="category-name">任务管理</span>
                            <span class="category-count">${taskCount}</span>
                        </a>
                    </li>
                `;
                
                document.getElementById('sidebarPreview').innerHTML = todoHtml;
                log('sidebarOutput', 'ToDo 入口 HTML 已生成并显示在预览区域');
            } else {
                log('sidebarOutput', 'ToDo 入口不应该显示');
            }
        }
        
        function testTodoEntry() {
            log('sidebarOutput', '测试 ToDo 入口生成...');
            
            // 直接生成 ToDo 入口 HTML
            const todoEntryHtml = `
                <li class="category-item">
                    <a href="#" class="category-link" data-category-id="todo">
                        <span class="category-icon">📋</span>
                        <span class="category-name">任务管理</span>
                        <span class="category-count">3</span>
                    </a>
                </li>
            `;
            
            document.getElementById('sidebarPreview').innerHTML = todoEntryHtml;
            log('sidebarOutput', 'ToDo 入口已添加到预览区域');
            log('sidebarOutput', 'HTML 内容:');
            log('sidebarOutput', todoEntryHtml);
        }
        
        function manualAddTodoEntry() {
            log('manualOutput', '尝试手动添加 ToDo 入口到主页面...');
            
            // 尝试找到主页面的侧边栏
            const mainWindow = window.parent !== window ? window.parent : window;
            const categoryList = mainWindow.document.getElementById('categoryList');
            
            if (categoryList) {
                log('manualOutput', '找到主页面的 categoryList');
                
                // 检查是否已存在 ToDo 入口
                const existingTodo = categoryList.querySelector('[data-category-id="todo"]');
                if (existingTodo) {
                    log('manualOutput', 'ToDo 入口已存在');
                    return;
                }
                
                // 创建 ToDo 入口
                const todoItem = mainWindow.document.createElement('li');
                todoItem.className = 'category-item';
                todoItem.innerHTML = `
                    <a href="#" class="category-link" data-category-id="todo"
                       data-has-children="false" data-level="0" role="button" tabindex="0" aria-label="任务管理">
                        <span class="category-icon" aria-hidden="true">📋</span>
                        <span class="category-name">任务管理</span>
                        <span class="category-count">0</span>
                    </a>
                `;
                
                // 插入到合适位置（在"常用"之后）
                const frequentItem = categoryList.querySelector('[data-category-id="frequent"]');
                if (frequentItem) {
                    frequentItem.parentNode.insertBefore(todoItem, frequentItem.nextSibling);
                    log('manualOutput', 'ToDo 入口已添加到主页面侧边栏');
                } else {
                    categoryList.appendChild(todoItem);
                    log('manualOutput', 'ToDo 入口已添加到侧边栏末尾');
                }
                
                // 添加点击事件
                const todoLink = todoItem.querySelector('.category-link');
                todoLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    log('manualOutput', 'ToDo 入口被点击！');
                    alert('ToDo 入口被点击！这里应该显示 ToDo 界面。');
                });
                
            } else {
                log('manualOutput', '未找到主页面的 categoryList');
                log('manualOutput', '请在主页面中打开此测试页面');
            }
        }
        
        // 页面加载时自动检查配置
        document.addEventListener('DOMContentLoaded', () => {
            checkConfig();
        });
    </script>
</body>
</html>
