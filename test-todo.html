<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ToDo 模块测试</title>
    <link rel="stylesheet" href="nav/css/bootstrap.min.css">
    <link rel="stylesheet" href="nav/fontawesome/css/all.min.css">
    <link rel="stylesheet" href="nav/css/style.css">
    <link rel="stylesheet" href="nav/css/themes.css">
    <link rel="stylesheet" href="nav/css/todo.css">
    <style>
        body {
            padding: 20px;
            background: #f8f9fa;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .test-header {
            background: #0078d4;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .test-content {
            padding: 20px;
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .test-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        .test-btn.primary {
            background: #0078d4;
            color: white;
        }
        .test-btn.secondary {
            background: #6c757d;
            color: white;
        }
        .test-btn:hover {
            opacity: 0.8;
        }
        .test-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        #todoContainer {
            margin-top: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
        }
    </style>
</head>
<body data-theme="ivory-light">
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-tasks"></i> ToDo 模块测试</h1>
            <p>测试 FaciShare 导航的 ToDo 功能模块</p>
        </div>
        
        <div class="test-content">
            <div class="test-buttons">
                <button class="test-btn primary" onclick="initTodo()">
                    <i class="fas fa-play"></i> 初始化 ToDo
                </button>
                <button class="test-btn secondary" onclick="showTodo()">
                    <i class="fas fa-eye"></i> 显示 ToDo
                </button>
                <button class="test-btn secondary" onclick="hideTodo()">
                    <i class="fas fa-eye-slash"></i> 隐藏 ToDo
                </button>
                <button class="test-btn secondary" onclick="addTestTask()">
                    <i class="fas fa-plus"></i> 添加测试任务
                </button>
                <button class="test-btn secondary" onclick="showStorage()">
                    <i class="fas fa-database"></i> 查看存储
                </button>
                <button class="test-btn secondary" onclick="clearStorage()">
                    <i class="fas fa-trash"></i> 清空存储
                </button>
            </div>
            
            <div class="test-output" id="output">等待操作...</div>
            
            <!-- ToDo 容器 -->
            <div id="todoContainer"></div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="nav/js/utils.js"></script>
    <script src="nav/js/todo/todo-utils.js"></script>
    <script src="nav/js/todo/todo-storage.js"></script>
    <script src="nav/js/todo/todo-manager.js"></script>
    
    <script>
        let todoManager = null;
        
        function log(message) {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            output.textContent += `[${timestamp}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
            console.log(message);
        }
        
        function initTodo() {
            try {
                log('开始初始化 ToDo 管理器...');
                
                // 模拟 navApp 对象
                const mockNavApp = {
                    config: {
                        todoModule: {
                            enabled: true,
                            showInSidebar: true
                        }
                    }
                };
                
                todoManager = new TodoManager(mockNavApp);
                log('ToDo 管理器初始化成功');
                
                // 显示统计信息
                setTimeout(() => {
                    const stats = {
                        tasks: todoManager.tasks.length,
                        lists: todoManager.lists.length,
                        settings: Object.keys(todoManager.settings).length
                    };
                    log(`加载数据: ${stats.tasks} 个任务, ${stats.lists} 个列表`);
                }, 100);
                
            } catch (error) {
                log(`初始化失败: ${error.message}`);
                console.error(error);
            }
        }
        
        function showTodo() {
            if (!todoManager) {
                log('请先初始化 ToDo 管理器');
                return;
            }
            
            try {
                todoManager.show();
                log('显示 ToDo 界面');
            } catch (error) {
                log(`显示失败: ${error.message}`);
            }
        }
        
        function hideTodo() {
            if (!todoManager) {
                log('请先初始化 ToDo 管理器');
                return;
            }
            
            try {
                todoManager.hide();
                log('隐藏 ToDo 界面');
            } catch (error) {
                log(`隐藏失败: ${error.message}`);
            }
        }
        
        function addTestTask() {
            if (!todoManager) {
                log('请先初始化 ToDo 管理器');
                return;
            }
            
            try {
                const testTask = {
                    title: `测试任务 ${Date.now()}`,
                    description: '这是一个测试任务，用于验证 ToDo 功能',
                    priority: {
                        level: 'medium',
                        setAt: new Date().toISOString(),
                        setBy: 'user'
                    },
                    dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 明天
                    important: Math.random() > 0.5
                };
                
                const task = todoManager.createTask(testTask);
                log(`创建测试任务: ${task.title} (ID: ${task.id})`);
                
                // 刷新界面
                if (todoManager.isVisible) {
                    todoManager.refreshUI();
                }
                
            } catch (error) {
                log(`创建任务失败: ${error.message}`);
            }
        }
        
        function showStorage() {
            if (!todoManager) {
                log('请先初始化 ToDo 管理器');
                return;
            }
            
            try {
                const storageInfo = todoManager.storage.getStorageInfo();
                log(`存储使用情况: ${storageInfo.usedFormatted} / ${storageInfo.availableFormatted} (${storageInfo.percentage}%)`);
                
                const allData = todoManager.storage.getAll();
                log(`数据详情:`);
                log(`- 任务: ${allData.tasks.length} 个`);
                log(`- 列表: ${allData.lists.length} 个`);
                log(`- 归档: ${allData.archivedTasks.length} 个`);
                
            } catch (error) {
                log(`获取存储信息失败: ${error.message}`);
            }
        }
        
        function clearStorage() {
            if (!todoManager) {
                log('请先初始化 ToDo 管理器');
                return;
            }
            
            if (confirm('确定要清空所有 ToDo 数据吗？')) {
                try {
                    todoManager.storage.clearAll();
                    log('已清空所有存储数据');
                    
                    // 重新初始化
                    setTimeout(() => {
                        initTodo();
                    }, 100);
                    
                } catch (error) {
                    log(`清空存储失败: ${error.message}`);
                }
            }
        }
        
        // 页面加载完成后自动初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('页面加载完成，准备测试 ToDo 模块');
        });
    </script>
</body>
</html>
