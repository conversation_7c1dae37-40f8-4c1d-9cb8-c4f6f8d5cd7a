/**
 * ToDo 主管理器
 * 统一管理所有 ToDo 功能，与 NavApp 集成
 */
class TodoManager {
    constructor(navApp) {
        this.navApp = navApp;
        this.storage = new TodoStorage();
        
        // 数据
        this.tasks = [];
        this.lists = [];
        this.archivedTasks = [];
        this.settings = {};
        
        // 子管理器
        this.subtaskManager = null;
        this.statusManager = null;
        this.priorityManager = null;
        this.archiveManager = null;
        this.countdownManager = null;
        this.memoManager = null;
        this.dataManager = null;
        
        // 事件监听器
        this.eventListeners = new Map();
        
        // UI 元素
        this.todoContainer = null;
        this.isVisible = false;
        
        this.init();
    }
    
    /**
     * 初始化 ToDo 管理器
     */
    async init() {
        try {
            // 加载数据
            await this.loadData();
            
            // 初始化子管理器
            this.initializeSubManagers();
            
            // 创建 UI
            this.createUI();
            
            // 绑定事件
            this.bindEvents();
            
            console.log('ToDo 管理器初始化完成');
        } catch (error) {
            console.error('ToDo 管理器初始化失败:', error);
        }
    }
    
    /**
     * 加载数据
     */
    async loadData() {
        const data = this.storage.getAll();
        
        this.tasks = data.tasks || [];
        this.lists = data.lists || [];
        this.archivedTasks = data.archivedTasks || [];
        this.settings = data.settings || {};
        
        console.log(`加载了 ${this.tasks.length} 个任务，${this.lists.length} 个列表`);
    }
    
    /**
     * 初始化子管理器
     */
    initializeSubManagers() {
        // 这些管理器将在后续步骤中实现
        // this.subtaskManager = new SubtaskManager(this);
        // this.statusManager = new TodoStatusManager(this);
        // this.priorityManager = new TodoPriorityManager(this);
        // this.archiveManager = new TodoArchiveManager(this);
        // this.countdownManager = new CountdownManager(this);
        // this.memoManager = new MemoManager(this);
        // this.dataManager = new TodoDataManager(this);
    }
    
    /**
     * 创建 UI
     */
    createUI() {
        // 检查是否已存在 ToDo 容器
        this.todoContainer = document.getElementById('todoContainer');
        
        if (!this.todoContainer) {
            this.todoContainer = document.createElement('div');
            this.todoContainer.id = 'todoContainer';
            this.todoContainer.className = 'todo-container';
            this.todoContainer.style.display = 'none';
            
            // 插入到主内容区域
            const mainContainer = document.querySelector('.main-container');
            if (mainContainer) {
                mainContainer.appendChild(this.todoContainer);
            }
        }
        
        this.renderUI();
    }
    
    /**
     * 渲染 UI
     */
    renderUI() {
        if (!this.todoContainer) return;
        
        this.todoContainer.innerHTML = `
            <div class="todo-header">
                <div class="todo-title">
                    <h2><i class="fas fa-tasks"></i> 任务管理</h2>
                    <div class="todo-stats">
                        <span class="stat-item">
                            <span class="stat-number">${this.getActiveTasksCount()}</span>
                            <span class="stat-label">活动任务</span>
                        </span>
                        <span class="stat-item">
                            <span class="stat-number">${this.getCompletedTasksCount()}</span>
                            <span class="stat-label">已完成</span>
                        </span>
                    </div>
                </div>
                <div class="todo-actions">
                    <button class="btn-primary add-task-btn">
                        <i class="fas fa-plus"></i> 新建任务
                    </button>
                    <button class="btn-secondary todo-settings-btn">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>
            </div>
            
            <div class="todo-content">
                <div class="todo-sidebar">
                    <nav class="todo-nav">
                        <ul class="todo-nav-list">
                            <li class="nav-item active" data-view="today">
                                <i class="fas fa-calendar-day"></i>
                                <span>今天</span>
                                <span class="nav-count">${this.getTodayTasksCount()}</span>
                            </li>
                            <li class="nav-item" data-view="important">
                                <i class="fas fa-star"></i>
                                <span>重要</span>
                                <span class="nav-count">${this.getImportantTasksCount()}</span>
                            </li>
                            <li class="nav-item" data-view="planned">
                                <i class="fas fa-calendar-alt"></i>
                                <span>已计划</span>
                                <span class="nav-count">${this.getPlannedTasksCount()}</span>
                            </li>
                            <li class="nav-item" data-view="all">
                                <i class="fas fa-list"></i>
                                <span>全部任务</span>
                                <span class="nav-count">${this.getActiveTasksCount()}</span>
                            </li>
                        </ul>
                        
                        <div class="todo-lists">
                            <div class="lists-header">
                                <h4>我的列表</h4>
                                <button class="add-list-btn">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                            <ul class="lists-container">
                                ${this.renderLists()}
                            </ul>
                        </div>
                    </nav>
                </div>
                
                <div class="todo-main">
                    <div class="todo-view-header">
                        <h3 class="view-title">今天</h3>
                        <div class="view-actions">
                            <button class="sort-btn" title="排序">
                                <i class="fas fa-sort"></i>
                            </button>
                            <button class="filter-btn" title="筛选">
                                <i class="fas fa-filter"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="tasks-container">
                        ${this.renderTasks('today')}
                    </div>
                </div>
            </div>
        `;
        
        this.bindUIEvents();
    }
    
    /**
     * 渲染任务列表
     */
    renderLists() {
        return this.lists.map(list => `
            <li class="list-item" data-list-id="${list.id}">
                <i class="${list.icon}" style="color: ${list.color}"></i>
                <span class="list-name">${list.name}</span>
                <span class="list-count">${this.getListTasksCount(list.id)}</span>
            </li>
        `).join('');
    }
    
    /**
     * 渲染任务
     */
    renderTasks(view = 'today') {
        const tasks = this.getTasksByView(view);
        
        if (tasks.length === 0) {
            return `
                <div class="empty-state">
                    <i class="fas fa-tasks fa-3x"></i>
                    <h3>暂无任务</h3>
                    <p>点击"新建任务"开始添加您的第一个任务</p>
                </div>
            `;
        }
        
        return tasks.map(task => this.renderTask(task)).join('');
    }
    
    /**
     * 渲染单个任务
     */
    renderTask(task) {
        const urgency = TodoUtils.getUrgencyLevel(task);
        const progress = TodoUtils.calculateProgress(task);
        const priorityConfig = this.getPriorityConfig(task.priority?.level);
        
        return `
            <div class="task-item" data-task-id="${task.id}" data-status="${task.status || 'not-started'}" data-urgency="${urgency}">
                <div class="task-checkbox">
                    <input type="checkbox" ${task.completed ? 'checked' : ''}>
                    <span class="checkmark"></span>
                </div>
                
                <div class="task-content">
                    <div class="task-header">
                        <h4 class="task-title">${task.title}</h4>
                        ${task.priority ? `
                            <div class="task-priority" style="color: ${priorityConfig.color}">
                                <i class="${priorityConfig.icon}"></i>
                            </div>
                        ` : ''}
                    </div>
                    
                    ${task.description ? `
                        <p class="task-description">${task.description}</p>
                    ` : ''}
                    
                    <div class="task-meta">
                        ${task.dueDate ? `
                            <span class="task-due-date ${urgency}">
                                <i class="fas fa-clock"></i>
                                ${TodoUtils.formatRelativeTime(task.dueDate)}
                            </span>
                        ` : ''}
                        
                        ${task.steps && task.steps.length > 0 ? `
                            <span class="task-progress">
                                <i class="fas fa-list-ul"></i>
                                ${progress}% (${this.getCompletedStepsCount(task)}/${task.steps.length})
                            </span>
                        ` : ''}
                        
                        ${task.memo && task.memo.content ? `
                            <span class="task-memo">
                                <i class="fas fa-sticky-note"></i>
                                ${task.memo.wordCount} 字
                            </span>
                        ` : ''}
                    </div>
                </div>
                
                <div class="task-actions">
                    <button class="task-action-btn edit-btn" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="task-action-btn more-btn" title="更多">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>
            </div>
        `;
    }
    
    /**
     * 绑定 UI 事件
     */
    bindUIEvents() {
        if (!this.todoContainer) return;
        
        // 新建任务
        const addTaskBtn = this.todoContainer.querySelector('.add-task-btn');
        if (addTaskBtn) {
            addTaskBtn.addEventListener('click', () => this.showAddTaskDialog());
        }
        
        // 导航切换
        const navItems = this.todoContainer.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                const view = e.currentTarget.dataset.view;
                this.switchView(view);
            });
        });
        
        // 任务复选框
        this.todoContainer.addEventListener('change', (e) => {
            if (e.target.type === 'checkbox' && e.target.closest('.task-item')) {
                const taskId = e.target.closest('.task-item').dataset.taskId;
                this.toggleTaskCompletion(taskId);
            }
        });
        
        // 任务编辑
        this.todoContainer.addEventListener('click', (e) => {
            if (e.target.closest('.edit-btn')) {
                const taskId = e.target.closest('.task-item').dataset.taskId;
                this.showEditTaskDialog(taskId);
            }
        });
    }
    
    /**
     * 绑定全局事件
     */
    bindEvents() {
        // 监听配置变化
        if (this.navApp) {
            this.navApp.on?.('config-updated', () => {
                this.loadSettings();
            });
        }
    }
    
    /**
     * 显示 ToDo 界面
     */
    show() {
        if (this.todoContainer) {
            this.todoContainer.style.display = 'block';
            this.isVisible = true;
            
            // 隐藏其他内容
            const contentArea = document.querySelector('.content-area');
            if (contentArea) {
                contentArea.style.display = 'none';
            }
            
            this.emit('todo-shown');
        }
    }
    
    /**
     * 隐藏 ToDo 界面
     */
    hide() {
        if (this.todoContainer) {
            this.todoContainer.style.display = 'none';
            this.isVisible = false;
            
            // 显示原内容
            const contentArea = document.querySelector('.content-area');
            if (contentArea) {
                contentArea.style.display = 'block';
            }
            
            this.emit('todo-hidden');
        }
    }
    
    /**
     * 切换显示状态
     */
    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }
    
    // ==================== 任务管理方法 ====================
    
    /**
     * 创建新任务
     */
    createTask(taskData) {
        const task = {
            id: TodoUtils.generateId(),
            title: taskData.title,
            description: taskData.description || '',
            status: 'not-started',
            completed: false,
            archived: false,
            listId: taskData.listId || this.getDefaultListId(),
            priority: taskData.priority || null,
            dueDate: taskData.dueDate || null,
            reminderDate: taskData.reminderDate || null,
            important: taskData.important || false,
            tags: taskData.tags || [],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            completedAt: null,
            archivedAt: null,
            steps: [],
            memo: null,
            metadata: {
                source: 'local',
                version: 1,
                checksum: ''
            }
        };
        
        // 计算校验和
        task.metadata.checksum = TodoUtils.calculateChecksum(task);
        
        this.tasks.push(task);
        this.saveData();
        this.refreshUI();
        
        this.emit('task-created', { task });
        return task;
    }
    
    /**
     * 获取任务
     */
    getTask(taskId) {
        return this.tasks.find(task => task.id === taskId);
    }
    
    /**
     * 更新任务
     */
    updateTask(taskId, updates) {
        const task = this.getTask(taskId);
        if (!task) return false;
        
        Object.assign(task, updates, {
            updatedAt: new Date().toISOString()
        });
        
        // 重新计算校验和
        task.metadata.checksum = TodoUtils.calculateChecksum(task);
        
        this.saveData();
        this.refreshUI();
        
        this.emit('task-updated', { task, updates });
        return true;
    }
    
    /**
     * 删除任务
     */
    deleteTask(taskId) {
        const taskIndex = this.tasks.findIndex(task => task.id === taskId);
        if (taskIndex === -1) return false;
        
        const task = this.tasks[taskIndex];
        this.tasks.splice(taskIndex, 1);
        
        this.saveData();
        this.refreshUI();
        
        this.emit('task-deleted', { task });
        return true;
    }
    
    /**
     * 切换任务完成状态
     */
    toggleTaskCompletion(taskId) {
        const task = this.getTask(taskId);
        if (!task) return false;
        
        task.completed = !task.completed;
        task.status = task.completed ? 'completed' : 'not-started';
        task.completedAt = task.completed ? new Date().toISOString() : null;
        task.updatedAt = new Date().toISOString();
        
        this.saveData();
        this.refreshUI();
        
        this.emit('task-toggled', { task });
        return true;
    }
    
    // ==================== 数据获取方法 ====================
    
    getActiveTasksCount() {
        return this.tasks.filter(task => !task.completed && !task.archived).length;
    }
    
    getCompletedTasksCount() {
        return this.tasks.filter(task => task.completed && !task.archived).length;
    }
    
    getTodayTasksCount() {
        return this.tasks.filter(task => 
            !task.archived && TodoUtils.isToday(task.dueDate)
        ).length;
    }
    
    getImportantTasksCount() {
        return this.tasks.filter(task => 
            !task.archived && task.important
        ).length;
    }
    
    getPlannedTasksCount() {
        return this.tasks.filter(task => 
            !task.archived && task.dueDate
        ).length;
    }
    
    getListTasksCount(listId) {
        return this.tasks.filter(task => 
            !task.archived && task.listId === listId
        ).length;
    }
    
    getCompletedStepsCount(task) {
        if (!task.steps) return 0;
        return task.steps.filter(step => step.completed).length;
    }
    
    getDefaultListId() {
        const defaultList = this.lists.find(list => list.isDefault);
        return defaultList ? defaultList.id : (this.lists[0]?.id || null);
    }
    
    getPriorityConfig(priority) {
        const configs = {
            'low': { icon: 'fas fa-arrow-down', color: '#6c757d' },
            'medium': { icon: 'fas fa-minus', color: '#ffc107' },
            'high': { icon: 'fas fa-arrow-up', color: '#fd7e14' },
            'urgent': { icon: 'fas fa-exclamation-triangle', color: '#dc3545' }
        };
        return configs[priority] || configs.medium;
    }
    
    getTasksByView(view) {
        let filtered = this.tasks.filter(task => !task.archived);
        
        switch (view) {
            case 'today':
                filtered = filtered.filter(task => TodoUtils.isToday(task.dueDate));
                break;
            case 'important':
                filtered = filtered.filter(task => task.important);
                break;
            case 'planned':
                filtered = filtered.filter(task => task.dueDate);
                break;
            case 'completed':
                filtered = filtered.filter(task => task.completed);
                break;
            default:
                // 'all' - 显示所有活动任务
                break;
        }
        
        return TodoUtils.sortTasks(filtered, this.settings.sortBy);
    }
    
    // ==================== UI 方法 ====================
    
    switchView(view) {
        // 更新导航状态
        const navItems = this.todoContainer.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.classList.toggle('active', item.dataset.view === view);
        });
        
        // 更新视图标题
        const viewTitle = this.todoContainer.querySelector('.view-title');
        if (viewTitle) {
            const titles = {
                'today': '今天',
                'important': '重要',
                'planned': '已计划',
                'all': '全部任务'
            };
            viewTitle.textContent = titles[view] || '任务';
        }
        
        // 重新渲染任务
        const tasksContainer = this.todoContainer.querySelector('.tasks-container');
        if (tasksContainer) {
            tasksContainer.innerHTML = this.renderTasks(view);
        }
    }
    
    refreshUI() {
        if (this.isVisible) {
            this.renderUI();
        }
    }
    
    showAddTaskDialog() {
        this.showTaskDialog();
    }

    showEditTaskDialog(taskId) {
        const task = this.getTask(taskId);
        if (task) {
            this.showTaskDialog(task);
        }
    }

    /**
     * 显示任务编辑对话框
     */
    showTaskDialog(task = null) {
        const isEdit = !!task;
        const modal = document.createElement('div');
        modal.className = 'task-modal';
        modal.innerHTML = `
            <div class="modal-backdrop"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3>${isEdit ? '编辑任务' : '新建任务'}</h3>
                    <button class="modal-close-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form class="task-form">
                        <div class="form-group">
                            <label for="taskTitle">任务标题 *</label>
                            <input type="text" id="taskTitle" name="title" required
                                   value="${task ? task.title : ''}"
                                   placeholder="输入任务标题...">
                        </div>

                        <div class="form-group">
                            <label for="taskDescription">任务描述</label>
                            <textarea id="taskDescription" name="description"
                                      placeholder="输入任务描述...">${task ? task.description || '' : ''}</textarea>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="taskList">所属列表</label>
                                <select id="taskList" name="listId">
                                    ${this.lists.map(list => `
                                        <option value="${list.id}" ${task && task.listId === list.id ? 'selected' : ''}>
                                            ${list.name}
                                        </option>
                                    `).join('')}
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="taskPriority">优先级</label>
                                <select id="taskPriority" name="priority">
                                    <option value="">选择优先级</option>
                                    <option value="low" ${task?.priority?.level === 'low' ? 'selected' : ''}>低</option>
                                    <option value="medium" ${task?.priority?.level === 'medium' ? 'selected' : ''}>中</option>
                                    <option value="high" ${task?.priority?.level === 'high' ? 'selected' : ''}>高</option>
                                    <option value="urgent" ${task?.priority?.level === 'urgent' ? 'selected' : ''}>紧急</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="taskDueDate">截止日期</label>
                                <input type="datetime-local" id="taskDueDate" name="dueDate"
                                       value="${task && task.dueDate ? new Date(task.dueDate).toISOString().slice(0, 16) : ''}">
                            </div>

                            <div class="form-group">
                                <label for="taskReminder">提醒时间</label>
                                <input type="datetime-local" id="taskReminder" name="reminderDate"
                                       value="${task && task.reminderDate ? new Date(task.reminderDate).toISOString().slice(0, 16) : ''}">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="important" ${task && task.important ? 'checked' : ''}>
                                <span class="checkmark"></span>
                                标记为重要任务
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-secondary cancel-btn">取消</button>
                    <button type="button" class="btn-primary save-btn">${isEdit ? '保存' : '创建'}</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 绑定事件
        const closeBtn = modal.querySelector('.modal-close-btn');
        const cancelBtn = modal.querySelector('.cancel-btn');
        const saveBtn = modal.querySelector('.save-btn');
        const backdrop = modal.querySelector('.modal-backdrop');

        const closeModal = () => {
            document.body.removeChild(modal);
        };

        closeBtn.addEventListener('click', closeModal);
        cancelBtn.addEventListener('click', closeModal);
        backdrop.addEventListener('click', closeModal);

        saveBtn.addEventListener('click', () => {
            const form = modal.querySelector('.task-form');
            const formData = new FormData(form);
            const taskData = {
                title: formData.get('title').trim(),
                description: formData.get('description').trim(),
                listId: formData.get('listId'),
                dueDate: formData.get('dueDate') || null,
                reminderDate: formData.get('reminderDate') || null,
                important: formData.has('important')
            };

            // 处理优先级
            const priority = formData.get('priority');
            if (priority) {
                taskData.priority = {
                    level: priority,
                    setAt: new Date().toISOString(),
                    setBy: 'user'
                };
            }

            // 验证数据
            if (!taskData.title) {
                alert('请输入任务标题');
                return;
            }

            try {
                if (isEdit) {
                    this.updateTask(task.id, taskData);
                } else {
                    this.createTask(taskData);
                }
                closeModal();
            } catch (error) {
                console.error('保存任务失败:', error);
                alert('保存任务失败，请重试');
            }
        });

        // 聚焦到标题输入框
        setTimeout(() => {
            modal.querySelector('#taskTitle').focus();
        }, 100);
    }
    
    // ==================== 数据持久化 ====================
    
    saveData() {
        return this.storage.saveAll({
            tasks: this.tasks,
            lists: this.lists,
            settings: this.settings,
            archivedTasks: this.archivedTasks
        });
    }
    
    loadSettings() {
        this.settings = this.storage.getSettings();
    }
    
    // ==================== 事件系统 ====================
    
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }
    
    off(event, callback) {
        if (this.eventListeners.has(event)) {
            const listeners = this.eventListeners.get(event);
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }
    
    emit(event, data) {
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`事件处理器错误 (${event}):`, error);
                }
            });
        }
    }
}
