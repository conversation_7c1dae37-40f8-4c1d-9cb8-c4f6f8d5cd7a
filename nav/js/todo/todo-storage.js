/**
 * ToDo 数据存储管理器
 * 负责 localStorage 数据持久化、备份和恢复
 */
class TodoStorage {
    constructor() {
        this.storageKeys = {
            tasks: 'todo-tasks',
            lists: 'todo-lists',
            settings: 'todo-settings',
            archived: 'todo-archived',
            backup: 'todo-backup'
        };
        
        this.defaultSettings = {
            defaultView: 'today',
            showCompleted: true,
            sortBy: 'dueDate',
            theme: 'auto',
            autoArchive: true,
            autoArchiveDelay: 7,
            notifications: {
                enabled: true,
                beforeDue: [60, 30, 10],
                overdue: true
            }
        };
        
        this.init();
    }
    
    /**
     * 初始化存储
     */
    init() {
        // 检查并初始化默认数据
        this.initializeDefaultData();
        
        // 设置自动备份
        this.setupAutoBackup();
    }
    
    /**
     * 初始化默认数据
     */
    initializeDefaultData() {
        // 初始化设置
        if (!this.getSettings()) {
            this.saveSettings(this.defaultSettings);
        }
        
        // 初始化默认任务列表
        const lists = this.getLists();
        if (!lists || lists.length === 0) {
            const defaultList = {
                id: this.generateId(),
                name: '我的任务',
                color: '#0078d4',
                icon: 'fas fa-list',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                isDefault: true
            };
            this.saveLists([defaultList]);
        }
        
        // 初始化空任务数组
        if (!this.getTasks()) {
            this.saveTasks([]);
        }
        
        // 初始化空归档数组
        if (!this.getArchivedTasks()) {
            this.saveArchivedTasks([]);
        }
    }
    
    /**
     * 生成唯一ID
     */
    generateId() {
        return 'todo-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    }
    
    /**
     * 保存任务列表
     */
    saveTasks(tasks) {
        try {
            const data = {
                tasks: tasks,
                lastUpdated: new Date().toISOString(),
                version: '1.0'
            };
            localStorage.setItem(this.storageKeys.tasks, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error('保存任务失败:', error);
            return false;
        }
    }
    
    /**
     * 获取任务列表
     */
    getTasks() {
        try {
            const data = localStorage.getItem(this.storageKeys.tasks);
            if (!data) return [];
            
            const parsed = JSON.parse(data);
            return parsed.tasks || [];
        } catch (error) {
            console.error('获取任务失败:', error);
            return [];
        }
    }
    
    /**
     * 保存任务列表分组
     */
    saveLists(lists) {
        try {
            const data = {
                lists: lists,
                lastUpdated: new Date().toISOString(),
                version: '1.0'
            };
            localStorage.setItem(this.storageKeys.lists, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error('保存列表失败:', error);
            return false;
        }
    }
    
    /**
     * 获取任务列表分组
     */
    getLists() {
        try {
            const data = localStorage.getItem(this.storageKeys.lists);
            if (!data) return [];
            
            const parsed = JSON.parse(data);
            return parsed.lists || [];
        } catch (error) {
            console.error('获取列表失败:', error);
            return [];
        }
    }
    
    /**
     * 保存设置
     */
    saveSettings(settings) {
        try {
            const data = {
                settings: settings,
                lastUpdated: new Date().toISOString(),
                version: '1.0'
            };
            localStorage.setItem(this.storageKeys.settings, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error('保存设置失败:', error);
            return false;
        }
    }
    
    /**
     * 获取设置
     */
    getSettings() {
        try {
            const data = localStorage.getItem(this.storageKeys.settings);
            if (!data) return this.defaultSettings;
            
            const parsed = JSON.parse(data);
            return { ...this.defaultSettings, ...parsed.settings };
        } catch (error) {
            console.error('获取设置失败:', error);
            return this.defaultSettings;
        }
    }
    
    /**
     * 保存归档任务
     */
    saveArchivedTasks(archivedTasks) {
        try {
            const data = {
                archivedTasks: archivedTasks,
                lastUpdated: new Date().toISOString(),
                version: '1.0'
            };
            localStorage.setItem(this.storageKeys.archived, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error('保存归档任务失败:', error);
            return false;
        }
    }
    
    /**
     * 获取归档任务
     */
    getArchivedTasks() {
        try {
            const data = localStorage.getItem(this.storageKeys.archived);
            if (!data) return [];
            
            const parsed = JSON.parse(data);
            return parsed.archivedTasks || [];
        } catch (error) {
            console.error('获取归档任务失败:', error);
            return [];
        }
    }
    
    /**
     * 保存所有数据
     */
    saveAll(todoData) {
        const results = {
            tasks: this.saveTasks(todoData.tasks || []),
            lists: this.saveLists(todoData.lists || []),
            settings: this.saveSettings(todoData.settings || this.defaultSettings),
            archived: this.saveArchivedTasks(todoData.archivedTasks || [])
        };
        
        return Object.values(results).every(result => result === true);
    }
    
    /**
     * 获取所有数据
     */
    getAll() {
        return {
            tasks: this.getTasks(),
            lists: this.getLists(),
            settings: this.getSettings(),
            archivedTasks: this.getArchivedTasks()
        };
    }
    
    /**
     * 创建备份
     */
    createBackup() {
        try {
            const allData = this.getAll();
            const backup = {
                data: allData,
                createdAt: new Date().toISOString(),
                version: '1.0',
                type: 'manual'
            };
            
            localStorage.setItem(this.storageKeys.backup, JSON.stringify(backup));
            return backup;
        } catch (error) {
            console.error('创建备份失败:', error);
            return null;
        }
    }
    
    /**
     * 恢复备份
     */
    restoreBackup() {
        try {
            const backupData = localStorage.getItem(this.storageKeys.backup);
            if (!backupData) return false;
            
            const backup = JSON.parse(backupData);
            return this.saveAll(backup.data);
        } catch (error) {
            console.error('恢复备份失败:', error);
            return false;
        }
    }
    
    /**
     * 清空所有数据
     */
    clearAll() {
        try {
            Object.values(this.storageKeys).forEach(key => {
                localStorage.removeItem(key);
            });
            
            // 重新初始化默认数据
            this.initializeDefaultData();
            return true;
        } catch (error) {
            console.error('清空数据失败:', error);
            return false;
        }
    }
    
    /**
     * 设置自动备份
     */
    setupAutoBackup() {
        // 每24小时自动备份一次
        setInterval(() => {
            this.createBackup();
        }, 24 * 60 * 60 * 1000);
    }
    
    /**
     * 获取存储使用情况
     */
    getStorageInfo() {
        const info = {
            used: 0,
            available: 0,
            percentage: 0,
            details: {}
        };
        
        try {
            // 计算各部分数据大小
            Object.entries(this.storageKeys).forEach(([key, storageKey]) => {
                const data = localStorage.getItem(storageKey);
                const size = data ? new Blob([data]).size : 0;
                info.details[key] = {
                    size: size,
                    sizeFormatted: this.formatBytes(size)
                };
                info.used += size;
            });
            
            // 估算可用空间（localStorage 通常限制为 5-10MB）
            info.available = 5 * 1024 * 1024; // 假设 5MB 限制
            info.percentage = Math.round((info.used / info.available) * 100);
            info.usedFormatted = this.formatBytes(info.used);
            info.availableFormatted = this.formatBytes(info.available);
            
        } catch (error) {
            console.error('获取存储信息失败:', error);
        }
        
        return info;
    }
    
    /**
     * 格式化字节数
     */
    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}
