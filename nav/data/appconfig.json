{"version": "3.0", "description": "FaciShare 导航数据配置文件", "lastUpdated": "2025-08-11", "dataVersion": "2025081411", "dataSources": [{"id": "foneshare-faci", "name": "foneshare-faci主配置", "path": "nav/data/foneshare-faci.json", "enabled": true, "priority": 1, "description": "线上纷享云主配置", "domains": ["oss.foneshare.cn", "127.0.0.1", "************", "localhost"]}, {"id": "foneshare-cloud-saas", "name": "foneshare-cloud-saas主配置", "path": "nav/data/foneshare-cloud-saas.json", "enabled": true, "priority": 2, "description": "线上多云管理纷享云配置", "domains": ["oss.foneshare.cn", "127.0.0.1", "************", "localhost"]}, {"id": "foneshare-cloud-private", "name": "foneshare-cloud-private主配置", "path": "nav/data/foneshare-cloud-private.json", "enabled": true, "priority": 3, "description": "线上多云管理私有云环境配置", "domains": ["oss.foneshare.cn", "127.0.0.1", "************", "localhost"]}, {"id": "foneshare-cloud-other", "name": "foneshare-cloud-other主配置", "path": "nav/data/foneshare-cloud-other.json", "enabled": true, "priority": 4, "description": "线上多云管理其他云环境配置", "domains": ["oss.foneshare.cn", "127.0.0.1", "************", "localhost"]}, {"id": "firstshare", "name": "firstshare主配置", "path": "nav/data/firstshare.json", "enabled": true, "priority": 5, "description": "线下firstshare环境主配置", "domains": ["oss.firstshare.cn"]}, {"id": "work-tools", "name": "效率办公", "path": "nav/data/work-tools.json", "enabled": true, "priority": 6, "description": "效率办公", "domains": []}], "mergeStrategy": {"duplicateHandling": "merge", "categoryMerging": "append", "siteIdConflict": "<PERSON><PERSON><PERSON><PERSON>", "preserveOrder": true}, "validation": {"strictMode": false, "allowEmptyCategories": true, "requireUniqueIds": true}, "fallback": {"enabled": true, "defaultPath": "nav/data/firstshare.json", "onError": "fallback"}, "timeNotifications": {"enabled": false, "configPath": "data/time-notifications.json", "description": "时间范围自动提示配置", "autoLoad": false, "fallbackEnabled": false}, "frequentCategory": {"displayCount": 30, "storageCount": 40, "description": "常用分类记忆配置,displayCount: 显示数量,storageCount: 存储数量."}, "todoModule": {"enabled": true, "showInSidebar": true, "defaultView": "today", "allowDataExport": true, "maxTasksPerList": 1000, "autoBackup": true, "backupInterval": 24, "autoArchive": true, "autoArchiveDelay": 7, "subtasks": {"enabled": true, "maxDepth": 3, "defaultWeight": "auto", "autoProgress": true, "showProgressInList": true, "allowReordering": true, "allowDependencies": true, "completionRules": {"default": "all", "allowCustom": true}}, "countdown": {"enabled": true, "updateInterval": 1000, "showInTaskList": true, "showProgressBar": true, "notifications": {"enabled": true, "beforeDue": [60, 30, 10], "overdue": true}, "urgencyThresholds": {"urgent": 24, "warning": 168}}, "markdown": {"enabled": true, "autoSave": true, "autoSaveInterval": 2000, "maxHistoryVersions": 50, "defaultMode": "split", "enableImageUpload": false, "enableCodeHighlight": true}}}