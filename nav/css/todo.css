/**
 * ToDo 模块样式
 * 基础样式和布局
 */

/* ==================== CSS 变量定义 ==================== */
:root {
  /* ToDo 主色调 */
  --todo-primary-color: #0078d4;
  --todo-primary-hover: #106ebe;
  --todo-secondary-color: #6c757d;
  --todo-accent-color: #0078d4;
  
  /* 背景色 */
  --todo-bg: #ffffff;
  --todo-panel-bg: #ffffff;
  --todo-sidebar-bg: #f8f9fa;
  --todo-header-bg: #ffffff;
  --todo-modal-bg: #ffffff;
  --todo-input-bg: #ffffff;
  --todo-dropdown-bg: #ffffff;
  --todo-hover-bg: #f5f5f5;
  --todo-selected-bg: #e3f2fd;
  --todo-action-bg: #f8f9fa;
  
  /* 文本色 */
  --todo-text-primary: #212529;
  --todo-text-secondary: #6c757d;
  --todo-text-muted: #adb5bd;
  
  /* 边框色 */
  --todo-border-color: #dee2e6;
  --todo-border-light: #e9ecef;
  --todo-border-lighter: #f1f3f4;
  
  /* 状态色 */
  --todo-success-color: #28a745;
  --todo-warning-color: #ffc107;
  --todo-danger-color: #dc3545;
  --todo-info-color: #17a2b8;
  
  /* 倒计时色 */
  --todo-countdown-normal: #6c757d;
  --todo-countdown-warning: #ffc107;
  --todo-countdown-urgent: #fd7e14;
  --todo-countdown-overdue: #dc3545;
  
  /* 进度条色 */
  --todo-progress-bg: #e9ecef;
  --todo-progress-fill: #0078d4;
  
  /* 阴影 */
  --todo-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --todo-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --todo-shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* 深色主题变量 */
[data-theme="dark-obsidian"] {
  --todo-bg: #1a1a1a;
  --todo-panel-bg: #2d2d2d;
  --todo-sidebar-bg: #252525;
  --todo-header-bg: #2d2d2d;
  --todo-modal-bg: #2d2d2d;
  --todo-input-bg: #3a3a3a;
  --todo-dropdown-bg: #2d2d2d;
  --todo-hover-bg: #3a3a3a;
  --todo-selected-bg: #1e3a5f;
  --todo-action-bg: #3a3a3a;
  
  --todo-text-primary: #ffffff;
  --todo-text-secondary: #b0b0b0;
  --todo-text-muted: #808080;
  
  --todo-border-color: #404040;
  --todo-border-light: #353535;
  --todo-border-lighter: #2a2a2a;
}

/* ==================== 主容器 ==================== */
.todo-container {
  width: 100%;
  height: 100vh;
  background: var(--todo-bg);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* ==================== 头部 ==================== */
.todo-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: var(--todo-header-bg);
  border-bottom: 1px solid var(--todo-border-color);
  flex-shrink: 0;
}

.todo-title {
  display: flex;
  align-items: center;
  gap: 24px;
}

.todo-title h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--todo-text-primary);
  display: flex;
  align-items: center;
  gap: 8px;
}

.todo-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-number {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--todo-primary-color);
  line-height: 1;
}

.stat-label {
  font-size: 0.8rem;
  color: var(--todo-text-secondary);
  margin-top: 2px;
}

.todo-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* ==================== 按钮样式 ==================== */
.btn-primary {
  background: var(--todo-primary-color);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn-primary:hover {
  background: var(--todo-primary-hover);
  transform: translateY(-1px);
}

.btn-secondary {
  background: var(--todo-action-bg);
  color: var(--todo-text-secondary);
  border: 1px solid var(--todo-border-color);
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: var(--todo-hover-bg);
  color: var(--todo-text-primary);
}

/* ==================== 内容区域 ==================== */
.todo-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* ==================== 侧边栏 ==================== */
.todo-sidebar {
  width: 280px;
  background: var(--todo-sidebar-bg);
  border-right: 1px solid var(--todo-border-color);
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

.todo-nav {
  padding: 16px 0;
}

.todo-nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--todo-text-secondary);
  border-left: 3px solid transparent;
}

.nav-item:hover {
  background: var(--todo-hover-bg);
  color: var(--todo-text-primary);
}

.nav-item.active {
  background: var(--todo-selected-bg);
  color: var(--todo-primary-color);
  border-left-color: var(--todo-primary-color);
}

.nav-item i {
  width: 16px;
  text-align: center;
}

.nav-item span {
  flex: 1;
}

.nav-count {
  background: var(--todo-text-muted);
  color: white;
  font-size: 0.75rem;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

.nav-item.active .nav-count {
  background: var(--todo-primary-color);
}

/* ==================== 列表区域 ==================== */
.todo-lists {
  margin-top: 24px;
  padding: 0 16px;
}

.lists-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.lists-header h4 {
  margin: 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--todo-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.add-list-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  color: var(--todo-text-secondary);
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.add-list-btn:hover {
  background: var(--todo-hover-bg);
  color: var(--todo-text-primary);
}

.lists-container {
  list-style: none;
  margin: 0;
  padding: 0;
}

.list-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
  color: var(--todo-text-secondary);
}

.list-item:hover {
  background: var(--todo-hover-bg);
  color: var(--todo-text-primary);
}

.list-item.active {
  background: var(--todo-selected-bg);
  color: var(--todo-primary-color);
}

.list-name {
  flex: 1;
  font-size: 0.9rem;
}

.list-count {
  font-size: 0.75rem;
  color: var(--todo-text-muted);
}

/* ==================== 主内容区 ==================== */
.todo-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.todo-view-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid var(--todo-border-light);
}

.view-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--todo-text-primary);
}

.view-actions {
  display: flex;
  gap: 8px;
}

.sort-btn,
.filter-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: var(--todo-action-bg);
  color: var(--todo-text-secondary);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sort-btn:hover,
.filter-btn:hover {
  background: var(--todo-hover-bg);
  color: var(--todo-text-primary);
}

/* ==================== 任务容器 ==================== */
.tasks-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px 24px;
}

/* ==================== 任务项 ==================== */
.task-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: var(--todo-panel-bg);
  border: 1px solid var(--todo-border-light);
  border-radius: 8px;
  margin-bottom: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.task-item:hover {
  border-color: var(--todo-primary-color);
  box-shadow: var(--todo-shadow-sm);
}

.task-item[data-status="completed"] {
  opacity: 0.7;
}

.task-item[data-urgency="urgent"] {
  border-left: 4px solid var(--todo-countdown-urgent);
}

.task-item[data-urgency="overdue"] {
  border-left: 4px solid var(--todo-countdown-overdue);
}

/* ==================== 任务复选框 ==================== */
.task-checkbox {
  position: relative;
  flex-shrink: 0;
  margin-top: 2px;
}

.task-checkbox input[type="checkbox"] {
  width: 20px;
  height: 20px;
  margin: 0;
  opacity: 0;
  cursor: pointer;
}

.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: 20px;
  border: 2px solid var(--todo-border-color);
  border-radius: 4px;
  background: var(--todo-input-bg);
  transition: all 0.2s ease;
  pointer-events: none;
}

.task-checkbox input[type="checkbox"]:checked + .checkmark {
  background: var(--todo-primary-color);
  border-color: var(--todo-primary-color);
}

.task-checkbox input[type="checkbox"]:checked + .checkmark::after {
  content: '';
  position: absolute;
  left: 6px;
  top: 2px;
  width: 6px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

/* ==================== 任务内容 ==================== */
.task-content {
  flex: 1;
  min-width: 0;
}

.task-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.task-title {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
  color: var(--todo-text-primary);
  line-height: 1.4;
}

.task-item[data-status="completed"] .task-title {
  text-decoration: line-through;
  color: var(--todo-text-muted);
}

.task-priority {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
}

.task-description {
  margin: 4px 0 8px 0;
  font-size: 0.9rem;
  color: var(--todo-text-secondary);
  line-height: 1.4;
}

.task-meta {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  font-size: 0.8rem;
  color: var(--todo-text-secondary);
}

.task-due-date,
.task-progress,
.task-memo {
  display: flex;
  align-items: center;
  gap: 4px;
}

.task-due-date.urgent {
  color: var(--todo-countdown-urgent);
}

.task-due-date.overdue {
  color: var(--todo-countdown-overdue);
}

.task-due-date.warning {
  color: var(--todo-countdown-warning);
}

/* ==================== 任务操作 ==================== */
.task-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.task-item:hover .task-actions {
  opacity: 1;
}

.task-action-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: var(--todo-action-bg);
  color: var(--todo-text-secondary);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.task-action-btn:hover {
  background: var(--todo-primary-color);
  color: white;
}

/* ==================== 空状态 ==================== */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--todo-text-secondary);
}

.empty-state i {
  color: var(--todo-text-muted);
  margin-bottom: 16px;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  font-size: 1.25rem;
  color: var(--todo-text-primary);
}

.empty-state p {
  margin: 0;
  font-size: 0.9rem;
}

/* ==================== 任务编辑对话框 ==================== */
.task-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10002;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.modal-content {
  position: relative;
  width: 90vw;
  max-width: 600px;
  max-height: 80vh;
  background: var(--todo-modal-bg);
  border-radius: 12px;
  box-shadow: var(--todo-shadow-lg);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid var(--todo-border-color);
  background: var(--todo-header-bg);
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--todo-text-primary);
}

.modal-close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  color: var(--todo-text-secondary);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close-btn:hover {
  background: var(--todo-hover-bg);
  color: var(--todo-text-primary);
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid var(--todo-border-color);
  background: var(--todo-header-bg);
}

/* ==================== 表单样式 ==================== */
.task-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-group label {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--todo-text-primary);
}

.form-group input,
.form-group textarea,
.form-group select {
  padding: 10px 12px;
  border: 1px solid var(--todo-border-color);
  border-radius: 6px;
  background: var(--todo-input-bg);
  color: var(--todo-text-primary);
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--todo-primary-color);
  box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.1);
}

.form-group textarea {
  min-height: 80px;
  resize: vertical;
  font-family: inherit;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  color: var(--todo-text-primary);
}

.checkbox-label input[type="checkbox"] {
  width: 18px;
  height: 18px;
  margin: 0;
  opacity: 0;
  cursor: pointer;
}

.checkbox-label .checkmark {
  position: relative;
  width: 18px;
  height: 18px;
  border: 2px solid var(--todo-border-color);
  border-radius: 4px;
  background: var(--todo-input-bg);
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: var(--todo-primary-color);
  border-color: var(--todo-primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 1px;
  width: 5px;
  height: 9px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 768px) {
  .todo-sidebar {
    width: 100%;
    position: absolute;
    top: 0;
    left: -100%;
    height: 100%;
    z-index: 1000;
    transition: left 0.3s ease;
  }

  .todo-sidebar.open {
    left: 0;
  }

  .todo-main {
    width: 100%;
  }

  .todo-header {
    padding: 12px 16px;
  }

  .todo-stats {
    display: none;
  }

  .tasks-container {
    padding: 12px 16px;
  }

  .task-actions {
    opacity: 1; /* 移动端始终显示 */
  }

  .task-meta {
    flex-direction: column;
    gap: 4px;
  }

  .modal-content {
    width: 95vw;
    max-height: 90vh;
  }

  .modal-header,
  .modal-footer {
    padding: 16px 20px;
  }

  .modal-body {
    padding: 20px;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}
